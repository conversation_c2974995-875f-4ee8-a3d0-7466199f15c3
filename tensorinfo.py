import warnings
from enum import Enum, auto
import math
import json
import sys

from SIMTemplate import Primitive, PrimName, DataFormat, VectorProcess, TensorInfo, ConvInfo, NoCInfo

#####################################
##  Convert                        ##
##################################### 

from npu_constants import *          

def convert_OPTYPE_VectorProcess(optype: OPTYPE) -> VectorProcess:
    _map = {
        OPTYPE.ADD               : VectorProcess.ADD              ,
        OPTYPE.SUB               : VectorProcess.SUB              ,
        OPTYPE.RSUB              : VectorProcess.RSUB             ,
        OPTYPE.MIN               : VectorProcess.MIN              ,
        OPTYPE.MAX               : VectorProcess.MAX              ,
        OPTYPE.EQUAL             : VectorProcess.EQUAL            ,
        OPTYPE.NOT_EQUAL         : VectorProcess.NOT_EQUAL        ,
        OPTYPE.GREATER           : VectorProcess.GREATER          ,
        OPTYPE.GREATER_OR_EQUAL  : VectorProcess.GREATER_OR_EQUAL ,
        OPTYPE.LESS              : VectorProcess.LESS             ,
        OPTYPE.LESS_OR_EQUAL     : VectorProcess.LESS_OR_EQUAL    ,
        OPTYPE.LEFT_SHIFT        : VectorProcess.LEFT_SHIFT       ,
        OPTYPE.RIGHT_SHIFT_FLOOR : VectorProcess.RIGHT_SHIFT_FLOOR, # round mode
        OPTYPE.RIGHT_SHIFT_CEIL  : VectorProcess.RIGHT_SHIFT_CEIL ,
        OPTYPE.RIGHT_SHIFT_ROUND : VectorProcess.RIGHT_SHIFT_ROUND,
        OPTYPE.AND               : VectorProcess.AND              ,
        OPTYPE.OR                : VectorProcess.OR               ,
        OPTYPE.XOR               : VectorProcess.XOR              ,
        OPTYPE.MUL               : VectorProcess.MUL              
    }

    if optype not in _map:
        raise ValueError(f"Unsupported OPTYPE: {optype}") 
    return _map[optype]


def convert_PrimName(name: str) -> PrimName:
    _map = {
        "mask_group_drv"    : PrimName.GROUP_MASK   ,
        "wr_lmem_drv"       : PrimName.WR_LMEM      ,
        "rd_lmem_drv"       : PrimName.RD_LMEM      ,
        "switch_CIMC_drv"   : PrimName.SWCIMC       ,
        "sync_drv"          : PrimName.SYNC         ,
        "noc_src_drv"       : PrimName.NOC_SRC      ,
        "noc_dest_drv"      : PrimName.NOC_DEST     ,
        "noc_fence_drv"     : PrimName.NOC_FENCE    ,
        "tld_drv"           : PrimName.TLD          ,
        # "tld_trans_drv"     : PrimName.TLD_TRANS    ,             # *
        # "tld_index_drv"     : PrimName.TIDLD      ,
        "tst_drv"           : PrimName.TST          ,
        # "tst_index_drv"     : PrimName.TIDST      ,
        "bc_drv"            : PrimName.BC           ,
        "mv_drv"            : PrimName.MOV          ,
        "tran_drv"          : PrimName.TRANS        ,
        # "setCIMExp"         : PrimName.UNKNOWN      ,
        # "getCIMExp"         : PrimName.UNKNOWN      ,
        # "conv_drv"          : PrimName.CONV       ,
        "gemv_drv"          : PrimName.GEMV         ,
        "gemm_drv"          : PrimName.GEMM         ,
        "vvv_drv"           : PrimName.VV_V         ,
        "vsv_drv"           : PrimName.VS_V         ,
        "vs_drv"            : PrimName.V_S          ,
        "vv_drv"            : PrimName.V_V          
    }

    if name not in _map:
        raise ValueError(f"Unsupported inst name: {name}") 
    return _map[name]

def convert_dataformat(dtype: DTYPE, width: WIDTH) -> DataFormat:
    _map = {
        (DTYPE.TYPE_INT, WIDTH.WIDTH_4 ) : DataFormat.INT4 ,    
        (DTYPE.TYPE_INT, WIDTH.WIDTH_8 ) : DataFormat.INT8 ,    
        (DTYPE.TYPE_INT, WIDTH.WIDTH_16) : DataFormat.INT16,    
        (DTYPE.TYPE_INT, WIDTH.WIDTH_32) : DataFormat.INT32,    
        (DTYPE.TYPE_FP , WIDTH.WIDTH_16) : DataFormat.FP16 ,     
        (DTYPE.TYPE_FP , WIDTH.WIDTH_32) : DataFormat.FP32 ,     
        (DTYPE.TYPE_BF , WIDTH.WIDTH_16) : DataFormat.BF16 ,     
        (DTYPE.TYPE_BBF, WIDTH.WIDTH_16) : DataFormat.BBF16,    
    }

    if (dtype, width) not in _map:
        raise ValueError(f"Unsupported data format: {dtype.name},{width.name}") 
    return _map[(dtype, width)]


def convert_dtype(dtype: DTYPE) -> str:
    _map = {
        DTYPE.TYPE_INT : "INT",    
        DTYPE.TYPE_FP  : "FP",     
        DTYPE.TYPE_BF  : "BF",     
        DTYPE.TYPE_BBF : "BBF",    
    }
    if dtype not in _map:
        raise ValueError(f"Unsupported data type: {dtype.name}")
    return _map[dtype]

def convert_width(width: WIDTH) -> int:
    _map = {
        WIDTH.WIDTH_4  : 4,    
        WIDTH.WIDTH_8  : 8,    
        WIDTH.WIDTH_16 : 16,   
        WIDTH.WIDTH_32 : 32,   
    }
    
    if width not in _map:
        raise ValueError(f"Unsupported width: {width}")
    return _map[width]



def convert_compact_scalars(scalar, mask) -> list:
    res_scalars = []

    if isinstance(scalar, int):
        for i in range(NPU_CONFIG.NPU_NUM):
            if ((mask >> i) & 0x1) == 1:
                res_scalars.append(scalar)
    elif isinstance(scalar, list):
        assert len(scalar) == NPU_CONFIG.NPU_NUM
        for i in range(NPU_CONFIG.NPU_NUM):
            if ((mask >> i) & 0x1) == 1:
                res_scalars.append(scalar[i])
    else:
        raise ValueError("Unsupported type of scalar")
    
    return res_scalars


def flatten_dim0(dim0b, rem_dim0, width):
    '''
    real width = 1<< width
    '''
    dim0a = 256 / (1 << width)
    if rem_dim0 == 0:
        return int(dim0a * dim0b)
    else:
        return int(dim0a * (dim0b - 1) + rem_dim0)
        


def fill_TensorInfo_tld(prim: Primitive , info_dict:dict) ->tuple:
    tin1 = prim.tensor_in1
    tout = prim.tensor_out

    tin1.type = tout.type   = convert_dtype(info_dict['type'])
    tin1.width = tout.width = convert_width(info_dict['wd'])
    rem_dim0, size_dim0b = info_dict['rem_dim0'], info_dict['size_dim0b']

    dim0 = flatten_dim0(size_dim0b, rem_dim0, info_dict['wd']) # <TODO> use tin1.width for tin1 and tout

    tin1.size = (int(dim0), info_dict['size_dim1'], info_dict['size_dim2'])
    tout.size = (int(dim0), info_dict['size_dim1'], info_dict['size_dim2'])
    tin1.byte_stride = (info_dict['stride_dim1_gmem'] * 32, info_dict['stride_dim2_gmem'] * 32)
    tout.byte_stride = (info_dict['stride_dim1_lmem'] * 32, info_dict['stride_dim2_lmem'] * 32)
    tin1.byte_base = info_dict['base_addr_gmem']
    tout.byte_base = info_dict['base_addr_lmem']


def fill_TensorInfo_tst(prim: Primitive, info_dict:dict) ->tuple:
    tin1 = prim.tensor_in1
    tout = prim.tensor_out

    tin1.type = tout.type   = convert_dtype(info_dict['type'])
    tin1.width = tout.width = convert_width(info_dict['wd'])
    rem_dim0, size_dim0b = info_dict['rem_dim0'], info_dict['size_dim0b']
    dim0 = flatten_dim0(size_dim0b, rem_dim0, info_dict['wd']) # <TODO> use tin1.width for tin1 and tout

    tin1.size = (int(dim0), info_dict['size_dim1'], info_dict['size_dim2'])
    tout.size = (int(dim0), info_dict['size_dim1'], info_dict['size_dim2'])
    tin1.byte_stride = (info_dict['stride_dim1_lmem'] * 32, info_dict['stride_dim2_lmem'] * 32)
    tout.byte_stride = (info_dict['stride_dim1_gmem'] * 32, info_dict['stride_dim2_gmem'] * 32)
    tin1.byte_base = info_dict['base_addr_lmem']
    tout.byte_base = info_dict['base_addr_gmem']


def fill_TensorInfo_tm(prim: Primitive, info_dict:dict) ->tuple:
    tin1 = prim.tensor_in1
    tout = prim.tensor_out

    tin1.type = tout.type   = convert_dtype(info_dict['type'])
    tin1.width = tout.width = convert_width(info_dict['wd'])
    rem_dim0_in, size_dim0b_in   = info_dict['rem_dim0_in'], info_dict['size_dim0b_in'] # <TODO> here use dim0 of in
    rem_dim0_out, size_dim0b_out = info_dict['rem_dim0_out'], info_dict['size_dim0b_out'] 

    dim0_in  = flatten_dim0(size_dim0b_in, rem_dim0_in, info_dict['wd']) 
    dim0_out = flatten_dim0(size_dim0b_out,rem_dim0_out,info_dict['wd'])

    tin1.size = (int(dim0_in), info_dict['size_dim1'], info_dict['size_dim2'])
    if prim.type == PrimName.TRANS:
        tout.size = (info_dict['size_dim1'], int(dim0_in), info_dict['size_dim2']) # warning: must use dim0_in here
    elif prim.type == PrimName.MOV:
        tout.size = (int(dim0_out), info_dict['size_dim1'], info_dict['size_dim2']) # warning: must use dim0_out here
    elif prim.type == PrimName.BC:
        tout.size = (int(dim0_out), info_dict['size_dim1'], info_dict['size_dim2']) # warning: must use dim0_in here
    else:
        raise ValueError(f'Unsupported prim.type: {prim.type}')


    print(f"fill_TensorInfo_tm: info_dict{json.dumps(info_dict)}")
    print(f"fill_TensorInfo_tm: tin1_size{tin1.size}")
    print(f"fill_TensorInfo_tm: tout_size{tout.size}")

    tin1.byte_stride = (info_dict['stride_dim1_in']  * 32, info_dict['stride_dim2_in']  * 32)
    tout.byte_stride = (info_dict['stride_dim1_out'] * 32, info_dict['stride_dim2_out'] * 32)
    if 'base_addr_in' in info_dict.keys():
        tin1.byte_base = info_dict['base_addr_in']
    else:
        tin1.byte_base = None
    tout.byte_base = info_dict['base_addr_out']



def fill_TensorInfo_tp(prim: Primitive, info_dict:dict) ->tuple:
    tin   = prim.tensor_in1
    tout  = prim.tensor_out
    torig = prim.tensor_orig

    tin.type, tout.type, torig.type =   convert_dtype(info_dict['type_in']), convert_dtype(info_dict['type_out']), convert_dtype(info_dict['type_orig'])
    tin.width, tout.width, torig.width =convert_width(info_dict['wd_in']  ), convert_width(info_dict['wd_out']  ), convert_width(info_dict['wd_orig']  )

    rem_dim0_in  , size_dim0b_in   = info_dict['rem_dim0_in'], info_dict['size_dim0b_in']
    rem_dim0_out , size_dim0b_out  = info_dict['rem_dim0_out'], info_dict['size_dim0b_out']

    dim0_in  = flatten_dim0(size_dim0b_in, rem_dim0_in, info_dict['wd_in'])
    dim0_out = flatten_dim0(size_dim0b_out,rem_dim0_out,info_dict['wd_out'])

    tin.size  = (int(dim0_in),  info_dict['size_dim1_in'],  info_dict['size_dim2_in'])
    tout.size = (int(dim0_out), info_dict['size_dim1_out'], info_dict['size_dim2_out'])
    torig.size= (int(dim0_out), info_dict['size_dim1_out'], info_dict['size_dim2_out'])

    tin.byte_stride   = (info_dict['stride_dim1_in']  * 32, info_dict['stride_dim2_in']  * 32)
    tout.byte_stride  = (info_dict['stride_dim1_out'] * 32, info_dict['stride_dim2_out'] * 32)
    torig.byte_stride = (info_dict['stride_dim1_out'] * 32, info_dict['stride_dim2_out'] * 32)

    tin.byte_base  = info_dict['base_addr_in']
    tout.byte_base = info_dict['base_addr_out']
    torig.byte_base= info_dict['base_addr_ori']


def fill_TensorInfo_vp(prim: Primitive, info_dict:dict) ->tuple:
    tin1 = prim.tensor_in1
    tin2 = prim.tensor_in2
    tout = prim.tensor_out

    tin1.type, tout.type    = convert_dtype(info_dict['type_in1']), convert_dtype(info_dict['type_out'])
    tin1.width,tout.width   = convert_width(info_dict['wd_in1']  ), convert_width(info_dict['wd_out'])

    rem_dim0_in1, size_dim0b_in1 = info_dict['rem_dim0_in1'], info_dict['size_dim0b_in1']
    rem_dim0_out, size_dim0b_out = info_dict['rem_dim0_out'], info_dict['size_dim0b_out']

    dim0_in1 = flatten_dim0(size_dim0b_in1, rem_dim0_in1, info_dict['wd_in1'])
    dim0_out = flatten_dim0(size_dim0b_out, rem_dim0_out, info_dict['wd_out'])

    tin1.size = (int(dim0_in1), 1, 1)
    tout.size = (int(dim0_out), 1, 1)
    
    tin1.byte_stride = (info_dict['size_dim0b_in1'] * 32, info_dict['size_dim0b_in1'] * 32)
    tout.byte_stride = (info_dict['size_dim0b_out'] * 32, info_dict['size_dim0b_out'] * 32)

    if prim.type == PrimName.VV_V:
        tin2.type = convert_dtype(info_dict['type_in2'])
        tin2.width= convert_width(info_dict['wd_in2']  )
        rem_dim0_in2, size_dim0b_in2 = info_dict['rem_dim0_in2'], info_dict['size_dim0b_in2']
        dim0_in2 = flatten_dim0(size_dim0b_in2, rem_dim0_in2, info_dict['wd_in2'])
        tin2.size = (int(dim0_in2), 1, 1)
        tin2.byte_stride = (info_dict['size_dim0b_in2'] * 32, info_dict['size_dim0b_in2'] * 32)
    if prim.type == PrimName.VS_V:
        tin2.type = convert_dtype(info_dict['type_in2'])
        tin2.width= convert_width(info_dict['wd_in2']  )


    # <TODO> ISA add special case info regs

    tin1.byte_base = info_dict['base_addr_in1']
    if 'base_addr_in2' in info_dict.keys():
        tin2.byte_base = info_dict['base_addr_in2']
    else:
        tin2.byte_base = None
    
    if 'base_addr_out' in info_dict.keys():
        tout.byte_base = info_dict['base_addr_out']
    else:
        tout.byte_base = None

def fill_TensorInfo_noc(prim: Primitive, info_dict:dict) ->tuple:
    t = prim.tensor_in1
    t.type  = convert_dtype(info_dict['type'])
    t.width = convert_width(info_dict['wd'])
    rem_dim0, size_dim0b = info_dict['rem_dim0'], info_dict['size_dim0b']
    dim0 = flatten_dim0(size_dim0b, rem_dim0, info_dict['width'])
    t.size = (int(dim0), info_dict['size_dim1'], info_dict['size_dim2'])
    t.byte_stride = (info_dict['stride_dim1'] * 32, info_dict['stride_dim2'] * 32)
    t.byte_base = info_dict['base_addr']


def fill_TensorInfo_noc_src(prim: Primitive, info_dict: dict) -> tuple:
    """Fill tensor info for NOC_SRC primitive"""
    # Create tensor if not exists
    if prim.tensor_in1 is None:
        prim.tensor_in1 = TensorInfo(prim)
    
    # Source tensor setup
    t = prim.tensor_in1
    t.type = convert_dtype(info_dict['type'])
    t.width = convert_width(info_dict['wd'])
    
    # Calculate dimensions
    rem_dim0, size_dim0b = info_dict['rem_dim0'], info_dict['size_dim0b']
    dim0 = flatten_dim0(size_dim0b, rem_dim0, info_dict['wd'])
    
    # Set tensor properties
    t.size = (int(dim0), info_dict['size_dim1'], info_dict['size_dim2'])
    t.byte_stride = (info_dict['stride_dim1'] * 32, info_dict['stride_dim2'] * 32)
    t.byte_base = info_dict['base_addr']
    
    # Calculate transfer size in bytes
    elements = int(dim0) * info_dict['size_dim1'] * info_dict['size_dim2']
    bytes_per_element = 2 if t.width == 16 else 4
    prim.noc_settings.transfer_size = elements * bytes_per_element
    
    # Set memory reference
    t.memory = prim.noc_settings.src_memory


def fill_TensorInfo_noc_dest(prim: Primitive, info_dict: dict) -> tuple:
    """Fill tensor info for NOC_DEST primitive"""
    # Create tensor if not exists
    if prim.tensor_out is None:
        prim.tensor_out = TensorInfo(prim)
    
    # Destination tensor setup
    t = prim.tensor_out
    t.type = convert_dtype(info_dict['type'])
    t.width = convert_width(info_dict['wd'])
    
    # Calculate dimensions
    rem_dim0, size_dim0b = info_dict['rem_dim0'], info_dict['size_dim0b']
    dim0 = flatten_dim0(size_dim0b, rem_dim0, info_dict['wd'])
    
    # Set tensor properties
    t.size = (int(dim0), info_dict['size_dim1'], info_dict['size_dim2'])
    t.byte_stride = (info_dict['stride_dim1'] * 32, info_dict['stride_dim2'] * 32)
    t.byte_base = info_dict['base_addr']
    
    # Calculate expected size in bytes
    elements = int(dim0) * info_dict['size_dim1'] * info_dict['size_dim2']
    bytes_per_element = 2 if t.width == 16 else 4
    prim.noc_settings.transfer_size = elements * bytes_per_element
    
    # Set memory reference
    t.memory = prim.noc_settings.dst_memory


def fill_TensorInfo(prim: Primitive,info_dict: dict) -> tuple:
    no_tensor = {
        PrimName.GROUP_MASK,
        PrimName.WR_LMEM, 
        PrimName.RD_LMEM, 
        PrimName.SWCIMC ,     
        PrimName.SYNC,
        PrimName.NOC_FENCE
    }
    noc_src = {
        PrimName.NOC_SRC
    }
    noc_dest = {
        PrimName.NOC_DEST
    }
    tld = {
        PrimName.TLD,
        PrimName.TLD_TRANS
    }
    tst = {
        PrimName.TST
    }
    tm = {
        PrimName.BC,
        PrimName.MOV,
        PrimName.TRANS
    }
    tp = {
        PrimName.GEMV,
        PrimName.GEMM
    }
    vp = {
        PrimName.VV_V, 
        PrimName.VS_V, 
        PrimName.V_S , 
        PrimName.V_V  
    }

    prim_name = prim.type

    if prim_name in no_tensor:
        pass
    elif prim_name in noc_src:
        fill_TensorInfo_noc_src(prim, info_dict)
    elif prim_name in noc_dest:
        fill_TensorInfo_noc_dest(prim, info_dict)
    elif prim_name in tld:
        fill_TensorInfo_tld(prim, info_dict)
    elif prim_name in tst:
        fill_TensorInfo_tst(prim, info_dict)
    elif prim_name in tm:
        fill_TensorInfo_tm(prim, info_dict)
    elif prim_name in tp:
        fill_TensorInfo_tp(prim, info_dict)
    elif prim_name in vp:
        fill_TensorInfo_vp(prim, info_dict)
    else:
        raise ValueError("Unsupported prim_name")


def convert_conv(prim: Primitive, info_dict):
    conv_settings = prim.conv_settings

    conv_settings.byte_base_wt = info_dict['base_addr_wt']
    conv_settings.type         = convert_dtype(info_dict['type_wt'])
    conv_settings.width        = convert_width(info_dict['wd_wt'])
    conv_settings.accu         = info_dict['accu']
    conv_settings.act          = info_dict['act']
    conv_settings.shift        = info_dict['shift']



def fill_nocinfo(prim: Primitive, info_dict) -> NoCInfo:
    noc_info = prim.noc_settings
    prim_name = prim.type

    if prim_name == PrimName.NOC_SRC:
        noc_info.is_src = True
        noc_info.src_addr = info_dict['base_addr']
        noc_info.dst_id   = info_dict['dest_idx']
        if isinstance(noc_info.dst_id, list):
            dst_core_id  = [((idx     ) & 0xff) for idx in noc_info.dst_id]
            dst_group_id = [((idx >> 8) & 0xff) for idx in noc_info.dst_id] 
            dst_chip_idx = [((idx >>16) & 0xff) for idx in noc_info.dst_id] 
            dst_chip_idy = [((idx >>24) & 0xff) for idx in noc_info.dst_id] 
        else:
            dst_core_id  = [((noc_info.dst_id     ) & 0xff)] * NPU_CONFIG.NPU_NUM
            dst_group_id = [((noc_info.dst_id >> 8) & 0xff)] * NPU_CONFIG.NPU_NUM 
            dst_chip_idx = [((noc_info.dst_id >>16) & 0xff)] * NPU_CONFIG.NPU_NUM 
            dst_chip_idy = [((noc_info.dst_id >>24) & 0xff)] * NPU_CONFIG.NPU_NUM 

        noc_info.dst_group = dst_group_id

    elif prim_name == PrimName.NOC_DEST:
        noc_info.is_src = False
        noc_info.dst_addr = info_dict['base_addr']
        noc_info.src_id = info_dict['src_idx']
        if isinstance(noc_info.src_id, list):
            src_core_id  = [((idx     ) & 0xff) for idx in noc_info.src_id]
            src_group_id = [((idx >> 8) & 0xff) for idx in noc_info.src_id] 
            src_chip_idx = [((idx >>16) & 0xff) for idx in noc_info.src_id] 
            src_chip_idy = [((idx >>24) & 0xff) for idx in noc_info.src_id] 
        else:
            src_core_id  = [((noc_info.src_id     ) & 0xff)] * NPU_CONFIG.NPU_NUM
            src_group_id = [((noc_info.src_id >> 8) & 0xff)] * NPU_CONFIG.NPU_NUM 
            src_chip_idx = [((noc_info.src_id >>16) & 0xff)] * NPU_CONFIG.NPU_NUM 
            src_chip_idy = [((noc_info.src_id >>24) & 0xff)] * NPU_CONFIG.NPU_NUM 

        noc_info.src_group = src_group_id

    else:
        pass

    return noc_info




def convert_Primitive(info_dict: dict, id = -1) -> Primitive:
    ret = Primitive(id)

    # just convert by order...

    prim_name = convert_PrimName(info_dict['primitive_name'])
    ret.type = prim_name
    fill_TensorInfo(ret, info_dict)


    if (prim_name == PrimName.GEMM) or (prim_name == PrimName.GEMV):
        convert_conv(ret, info_dict)


    if 'op' in info_dict.keys(): # is vp
        ret.vector_op = convert_OPTYPE_VectorProcess(info_dict['op'])
    else:
        ret.vector_op = None

    ret.module_use = [] # <TODO>
    ret.loop_num = 1


    fill_nocinfo(ret, info_dict)

    if 'cimc_mode' in info_dict.keys():
        ret.cimc_mode = info_dict['cimc_mode']

    ret.npu_group = info_dict['group']
    ret.npu_mask  = info_dict['mask']

    # ret.scalars = convert_compact_scalars(info_dict['scalars'], ret.npu_mask)   
    ret.scalars = info_dict['scalars']

    return ret





if __name__ == '__main__':
    for optype in OPTYPE:
        vp = convert_OPTYPE_VectorProcess(optype)
        print(f"{optype.name} -> {vp.name}")
        assert optype.name == vp.name


















