# Bug Report

## Bug Summary
[Provide a clear, concise description of the bug]

## Bug Details

### Expected Behavior
[Describe what should happen]

### Actual Behavior  
[Describe what actually happens]

### Steps to Reproduce
1. [Step 1]
2. [Step 2]
3. [Step 3]
4. [Observe the issue]

### Environment
- **Version**: [Application/system version]
- **Platform**: [OS, browser, device, etc.]
- **Configuration**: [Relevant settings or environment details]

## Impact Assessment

### Severity
- [ ] Critical - System unusable
- [ ] High - Major functionality broken
- [ ] Medium - Feature impaired but workaround exists
- [ ] Low - Minor issue or cosmetic

### Affected Users
[Who is impacted by this bug?]

### Affected Features
[What functionality is broken or impaired?]

## Additional Context

### Error Messages
```
[Include any error messages, stack traces, or logs]
```

### Screenshots/Media
[Describe any visual evidence or attach files]

### Related Issues
[Reference any related bugs, features, or discussions]

## Initial Analysis

### Suspected Root Cause
[Initial thoughts on what might be causing the issue]

### Affected Components
[List files, modules, or systems that might be involved]